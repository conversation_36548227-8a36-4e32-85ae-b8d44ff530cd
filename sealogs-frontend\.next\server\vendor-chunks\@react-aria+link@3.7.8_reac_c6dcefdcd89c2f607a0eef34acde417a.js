"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@react-aria+link@3.7.8_reac_c6dcefdcd89c2f607a0eef34acde417a";
exports.ids = ["vendor-chunks/@react-aria+link@3.7.8_reac_c6dcefdcd89c2f607a0eef34acde417a"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@react-aria+link@3.7.8_reac_c6dcefdcd89c2f607a0eef34acde417a/node_modules/@react-aria/link/dist/useLink.mjs":
/*!****************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-aria+link@3.7.8_reac_c6dcefdcd89c2f607a0eef34acde417a/node_modules/@react-aria/link/dist/useLink.mjs ***!
  \****************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useLink: () => (/* binding */ $298d61e98472621b$export$dcf14c9974fe2767)\n/* harmony export */ });\n/* harmony import */ var _react_aria_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @react-aria/utils */ \"(ssr)/./node_modules/.pnpm/@react-aria+utils@3.27.0_re_4bf881c7f169b3927cbbceda9562f024/node_modules/@react-aria/utils/dist/filterDOMProps.mjs\");\n/* harmony import */ var _react_aria_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @react-aria/utils */ \"(ssr)/./node_modules/.pnpm/@react-aria+utils@3.27.0_re_4bf881c7f169b3927cbbceda9562f024/node_modules/@react-aria/utils/dist/mergeProps.mjs\");\n/* harmony import */ var _react_aria_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @react-aria/utils */ \"(ssr)/./node_modules/.pnpm/@react-aria+utils@3.27.0_re_4bf881c7f169b3927cbbceda9562f024/node_modules/@react-aria/utils/dist/openLink.mjs\");\n/* harmony import */ var _react_aria_focus__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @react-aria/focus */ \"(ssr)/./node_modules/.pnpm/@react-aria+focus@3.19.1_re_f22b377d4e01f6fec11051c4684fbd09/node_modules/@react-aria/focus/dist/useFocusable.mjs\");\n/* harmony import */ var _react_aria_interactions__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @react-aria/interactions */ \"(ssr)/./node_modules/.pnpm/@react-aria+interactions@3._697c5b52c6876ffd3e2260945ec4674e/node_modules/@react-aria/interactions/dist/usePress.mjs\");\n\n\n\n\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ \n\n\nfunction $298d61e98472621b$export$dcf14c9974fe2767(props, ref) {\n    let { elementType: elementType = 'a', onPress: onPress, onPressStart: onPressStart, onPressEnd: onPressEnd, // @ts-ignore\n    onClick: deprecatedOnClick, isDisabled: isDisabled, ...otherProps } = props;\n    let linkProps = {};\n    if (elementType !== 'a') linkProps = {\n        role: 'link',\n        tabIndex: !isDisabled ? 0 : undefined\n    };\n    let { focusableProps: focusableProps } = (0, _react_aria_focus__WEBPACK_IMPORTED_MODULE_0__.useFocusable)(props, ref);\n    let { pressProps: pressProps, isPressed: isPressed } = (0, _react_aria_interactions__WEBPACK_IMPORTED_MODULE_1__.usePress)({\n        onPress: onPress,\n        onPressStart: onPressStart,\n        onPressEnd: onPressEnd,\n        isDisabled: isDisabled,\n        ref: ref\n    });\n    let domProps = (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_2__.filterDOMProps)(otherProps, {\n        labelable: true\n    });\n    let interactionHandlers = (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_3__.mergeProps)(focusableProps, pressProps);\n    let router = (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    let routerLinkProps = (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_4__.useLinkProps)(props);\n    return {\n        isPressed: isPressed,\n        linkProps: (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_3__.mergeProps)(domProps, routerLinkProps, {\n            ...interactionHandlers,\n            ...linkProps,\n            'aria-disabled': isDisabled || undefined,\n            'aria-current': props['aria-current'],\n            onClick: (e)=>{\n                var _pressProps_onClick;\n                (_pressProps_onClick = pressProps.onClick) === null || _pressProps_onClick === void 0 ? void 0 : _pressProps_onClick.call(pressProps, e);\n                if (deprecatedOnClick) {\n                    deprecatedOnClick(e);\n                    console.warn('onClick is deprecated, please use onPress');\n                }\n                // If a custom router is provided, prevent default and forward if this link should client navigate.\n                if (!router.isNative && e.currentTarget instanceof HTMLAnchorElement && e.currentTarget.href && // If props are applied to a router Link component, it may have already prevented default.\n                !e.isDefaultPrevented() && (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_4__.shouldClientNavigate)(e.currentTarget, e) && props.href) {\n                    e.preventDefault();\n                    router.open(e.currentTarget, e, props.href, props.routerOptions);\n                }\n            }\n        })\n    };\n}\n\n\n\n//# sourceMappingURL=useLink.module.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-aria+link@3.7.8_reac_c6dcefdcd89c2f607a0eef34acde417a/node_modules/@react-aria/link/dist/useLink.mjs\n");

/***/ })

};
;