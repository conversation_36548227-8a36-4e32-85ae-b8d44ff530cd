"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/inventory/page",{

/***/ "(app-pages-browser)/./src/components/ui/badge.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/badge.tsx ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Badge: function() { return /* binding */ Badge; },\n/* harmony export */   badgeVariants: function() { return /* binding */ badgeVariants; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(app-pages-browser)/./node_modules/.pnpm/class-variance-authority@0.7.1/node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n\n\n\n\nconst badgeVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center font-black border p-2\", {\n    variants: {\n        variant: {\n            default: \"border-primary bg-transparent\",\n            primary: \"text-curious-blue-500 border border-bltext-curious-blue-500 bg-bltext-curious-blue-100\",\n            success: \"text-bright-turquoise-600 border-bright-turquoise-600 bg-bright-turquoise-100\",\n            warning: \"text-fire-bush-500 border-fire-bush-500 bg-fire-bush-100\",\n            destructive: \"text-destructive border-destructive bg-cinnabar-200\",\n            outline: \"text-foreground border-border\",\n            secondary: \"border bg-muted text-muted-foreground w-fit rounded-lg p-2\"\n        },\n        type: {\n            normal: \"w-fit h-11 text-nowrap rounded-lg\",\n            circle: \"rounded-full justify-center h-9 w-9\"\n        }\n    },\n    defaultVariants: {\n        variant: \"primary\",\n        type: \"circle\"\n    }\n});\nfunction Badge(param) {\n    let { className, variant, type, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(badgeVariants({\n            variant,\n            type\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\badge.tsx\",\n        lineNumber: 37,\n        columnNumber: 9\n    }, this);\n}\n_c = Badge;\n\nvar _c;\n$RefreshReg$(_c, \"Badge\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/badge.tsx\n"));

/***/ })

});