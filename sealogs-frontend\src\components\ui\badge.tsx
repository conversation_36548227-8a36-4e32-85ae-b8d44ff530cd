import * as React from 'react'
import { cva, type VariantProps } from 'class-variance-authority'

import { cn } from '@/app/lib/utils'

const badgeVariants = cva('inline-flex items-center font-black border p-2', {
    variants: {
        variant: {
            default: 'border-primary bg-transparent',
            primary:
                'text-curious-blue-500 border border-bltext-curious-blue-500 bg-bltext-curious-blue-100',
            success:
                'text-bright-turquoise-600 border-bright-turquoise-600 bg-bright-turquoise-100',
            warning: 'text-fire-bush-500 border-fire-bush-500 bg-fire-bush-100',
            destructive: 'text-destructive border-destructive bg-cinnabar-200',
            outline: 'text-foreground border-border',
            secondary:
                'border bg-muted text-muted-foreground w-fit rounded-lg p-2',
        },
        type: {
            normal: 'w-fit h-11 text-nowrap rounded-lg',
            circle: 'rounded-full justify-center h-9 w-9',
        },
    },
    defaultVariants: {
        variant: 'primary',
        type: 'circle',
    },
})

export interface BadgeProps
    extends React.HTMLAttributes<HTMLDivElement>,
        VariantProps<typeof badgeVariants> {}

function Badge({ className, variant, type, ...props }: BadgeProps) {
    return (
        <div
            className={cn(badgeVariants({ variant, type }), className)}
            {...props}
        />
    )
}

export { Badge, badgeVariants }
