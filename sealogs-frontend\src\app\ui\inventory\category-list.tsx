'use client'

import React, { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import {
    Popover,
    PopoverContent,
    PopoverTrigger,
} from '@/components/ui/popover'
import { List, Tags } from 'lucide-react'
import Link from 'next/link'
import { useLazyQuery } from '@apollo/client'
import { GET_INVENTORY_CATEGORY } from '@/app/lib/graphQL/query'

import { DataTable, createColumns } from '@/components/filteredTable'
import { DataTableSortHeader } from '@/components/data-table-sort-header'
import { ListHeader } from '@/components/ui/list-header'
import { P, Badge } from '@/components/ui'
import Loading from '@/app/loading'
import { CategoryFilterActions } from '@/components/filter/components/category-actions'

export default function InventoryCategoryList() {
    const [categories, setCategories] = useState<any[]>([])
    const [isLoading, setIsLoading] = useState(true)

    const [queryCategories] = useLazyQuery(GET_INVENTORY_CATEGORY, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readInventoryCategories.nodes
            if (data) {
                setCategories(data)
            }
            setIsLoading(false)
        },
        onError: (error: any) => {
            console.error('queryCategories error', error)
            setIsLoading(false)
        },
    })

    useEffect(() => {
        if (isLoading) {
            queryCategories()
        }
    }, [isLoading, queryCategories])

    const columns = createColumns([
        {
            accessorKey: 'name',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Name" />
            ),
            cellAlignment: 'left',
            cell: ({ row }: { row: any }) => {
                const category = row.original
                return (
                    <div className="flex items-center">
                        <Link
                            href={`/settings/inventory/category?categoryID=${category.id}`}
                            className={`hover:underline ${
                                category.archived
                                    ? 'line-through text-muted-foreground'
                                    : ''
                            }`}>
                            {category.name}
                        </Link>
                        {category.archived && (
                            <Badge variant="secondary" className="ml-2 text-xs">
                                Archived
                            </Badge>
                        )}
                    </div>
                )
            },
            sortingFn: (rowA: any, rowB: any) => {
                const valueA = rowA?.original?.name || ''
                const valueB = rowB?.original?.name || ''
                return valueA.localeCompare(valueB)
            },
        },
        {
            accessorKey: 'abbreviation',
            header: 'Abbreviation',
            cell: ({ row }: { row: any }) => {
                const category = row.original
                return (
                    <div className="text-sm">
                        {category.abbreviation || '-'}
                    </div>
                )
            },
        },
        {
            accessorKey: 'inventories',
            header: 'Recent Inventories',
            cellAlignment: 'right',
            cell: ({ row }: { row: any }) => {
                const category = row.original
                const inventoryCount = category.inventories?.nodes?.length || 0

                if (inventoryCount === 0) {
                    return (
                        <span className="text-sm text-muted-foreground">-</span>
                    )
                }

                return (
                    <Popover>
                        <PopoverTrigger asChild>
                            <Button
                                variant="ghost"
                                size="sm"
                                className="h-8 w-8 p-0">
                                <List className="h-4 w-4" />
                            </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-80">
                            <div className="space-y-2">
                                <P className="font-medium text-sm">
                                    Inventories ({inventoryCount})
                                </P>
                                <div className="space-y-1 max-h-48 overflow-y-auto">
                                    {category.inventories.nodes.map(
                                        (inventory: any) => (
                                            <Link
                                                key={inventory.id}
                                                href={`/inventory/view?id=${inventory.id}`}
                                                className="block py-1 px-2 text-sm hover:bg-muted rounded">
                                                {inventory.title}
                                            </Link>
                                        ),
                                    )}
                                </div>
                            </div>
                        </PopoverContent>
                    </Popover>
                )
            },
        },
    ])

    return (
        <>
            <ListHeader
                icon={<Tags className="h-12 w-12 ring-1 p-1 rounded-full" />}
                title="Inventory categories"
                actions={<CategoryFilterActions />}
            />
            <div className="mt-16">
                {isLoading ? (
                    <Loading />
                ) : (
                    <DataTable
                        columns={columns}
                        data={categories}
                        showToolbar={true}
                        pageSize={20}
                    />
                )}
            </div>
        </>
    )
}
