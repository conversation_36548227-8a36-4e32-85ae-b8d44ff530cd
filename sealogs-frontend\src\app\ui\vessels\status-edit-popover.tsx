'use client'

import { useEffect, useState } from 'react'
import { useMutation } from '@apollo/client'
import { CREATE_VESSELSTATUS } from '@/app/lib/graphQL/mutation'
import Editor from '../editor'
import { AlertDialogNew, Combobox, Label, Textarea } from '@/components/ui'
import DatePicker from '@/components/DateRange'
import { useToast } from '@/hooks/use-toast'

const vesselStatuses: any = [
    { label: 'On Voyage', value: 'OnVoyage' },
    { label: 'Ready For Voyage', value: 'AvailableForVoyage' },
    { label: 'Out Of Service', value: 'OutOfService' },
]
const vesselStatusReason: any = [
    { label: 'Crew Unavailable', value: 'CrewUnavailable' },
    { label: 'Skipper/Master Unavailable', value: 'MasterUnavailable' },
    { label: 'Planned Maintenance', value: 'PlannedMaintenance' },
    { label: 'Breakdown', value: 'Breakdown' },
    { label: 'Other', value: 'Other' },
]

export default function VesselsStatusEdit({
    vessel,
    display,
    setDisplay,
}: any) {
    const { toast } = useToast()

    const [vesselStatus, setVesselStatus] = useState<any>(null)

    const handleUpdateVesselStatus = () => {
        vesselStatus?.status === 'OutOfService'
            ? createVesselStatus({
                  variables: {
                      input: {
                          vesselID: vessel?.id,
                          date: vesselStatus?.date,
                          status: vesselStatus?.status,
                          comment: vesselStatus?.comment,
                          reason: vesselStatus?.reason,
                          otherReason: vesselStatus?.otherReason,
                          expectedReturn: vesselStatus?.expectedReturn,
                      },
                  },
              })
            : createVesselStatus({
                  variables: {
                      input: {
                          vesselID: vessel?.id,
                          date: vesselStatus?.date,
                          status: vesselStatus?.status,
                      },
                  },
              })
    }

    const handleVesselStatusDate = (date: any) => {
        setVesselStatus({
            ...vesselStatus,
            date: date,
        })
    }

    const handleVesselStatusReturnDate = (date: any) => {
        setVesselStatus({
            ...vesselStatus,
            expectedReturn: date,
        })
    }

    const [createVesselStatus] = useMutation(CREATE_VESSELSTATUS, {
        onCompleted: (response: any) => {
            const data = response.createLogBookEntry
            setVesselStatus({
                ...vesselStatus,
                vesselID: vessel?.id,
                date: vesselStatus?.date,
                status: vesselStatus?.status,
                comment: vesselStatus?.comment,
                reason: vesselStatus?.reason,
                otherReason: vesselStatus?.otherReason,
                expectedReturn: vesselStatus?.expectedReturn,
            })
            setDisplay(false)
        },
        onError: (error: any) => {
            toast({
                variant: 'destructive',
                description: error.message,
            })
        },
    })

    useEffect(() => {
        if (display) {
            setDisplay(true)
        }
    }, [display])

    const handleVesselStatusChange = (value: any) => {
        if (vessel.logentryID !== 0) {
            value.value === 'OnVoyage'
                ? toast({
                      variant: 'destructive',
                      description:
                          'There is no Open LogBook entry, Please create a Logbook entry to set the vessel on voyage',
                  })
                : setVesselStatus({
                      ...vesselStatus,
                      status: value?.value,
                  })
        } else {
            value.value !== 'OnVoyage'
                ? toast({
                      variant: 'destructive',
                      description:
                          'There is an Open LogBook entry, Please complete the entry in order to update the vessel status',
                  })
                : setVesselStatus({
                      ...vesselStatus,
                      status: value?.value,
                  })
        }
    }

    const handleVesselStatusReasonChange = (value: any) => {
        setVesselStatus({
            ...vesselStatus,
            reason: value?.value,
        })
    }

    return (
        <AlertDialogNew
            openDialog={display}
            size="xl"
            setOpenDialog={setDisplay}
            handleCreate={handleUpdateVesselStatus}
            title="Update Vessel Status"
            actionText="Update">
            <div className="">
                <div className="mb-4 md:mb-0">
                    <div className="my-4">
                        <DatePicker
                            mode="single"
                            onChange={handleVesselStatusDate}
                            className="w-full"
                            placeholder="Select date"
                            value={vesselStatus?.date}
                        />
                    </div>
                    <div className="flex gap-2.5 my-4">
                        <Label label="Status" className="flex-1">
                            <Combobox
                                id="vessel-status"
                                options={vesselStatuses}
                                placeholder="Status"
                                value={vesselStatuses.find(
                                    (status: any) =>
                                        vesselStatus?.status === status.value,
                                )}
                                onChange={handleVesselStatusChange}
                            />
                        </Label>
                        {vesselStatus?.status === 'OutOfService' && (
                            <Label label="Reason for out of service status">
                                <Combobox
                                    id="vessel-status-reason"
                                    options={vesselStatusReason}
                                    placeholder="Reason"
                                    value={vesselStatusReason.find(
                                        (status: any) =>
                                            vesselStatus?.reason ===
                                            status.value,
                                    )}
                                    onChange={handleVesselStatusReasonChange}
                                />
                            </Label>
                        )}
                    </div>

                    {vesselStatus?.status === 'OutOfService' &&
                        vesselStatus?.reason === 'Other' && (
                            <div className="flex items-center my-4">
                                <Textarea
                                    id="vessel-status-other"
                                    placeholder="Other description"
                                    value={vesselStatus?.otherReason}
                                    onChange={(e) =>
                                        setVesselStatus({
                                            ...vesselStatus,
                                            otherReason: e.target.value,
                                        })
                                    }
                                />
                            </div>
                        )}
                    {vesselStatus?.status === 'OutOfService' && (
                        <Label label="Comments" className="my-4">
                            <Editor
                                id="comment"
                                placeholder="Comment"
                                content={vesselStatus?.comment}
                                handleEditorChange={(content: string) =>
                                    setVesselStatus({
                                        ...vesselStatus,
                                        comment: content,
                                    })
                                }
                            />
                        </Label>
                    )}
                    {vesselStatus?.status === 'OutOfService' && (
                        <div className="my-4">
                            <Label label="Expected date of return" />
                            <DatePicker
                                mode="single"
                                onChange={handleVesselStatusReturnDate}
                                className="w-full"
                                placeholder="Select date"
                                value={vesselStatus?.expectedReturn}
                            />
                        </div>
                    )}
                </div>
            </div>
        </AlertDialogNew>
    )
}
