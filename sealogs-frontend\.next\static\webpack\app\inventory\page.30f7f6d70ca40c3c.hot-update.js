"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/inventory/page",{

/***/ "(app-pages-browser)/./src/components/ui/badge.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/badge.tsx ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Badge: function() { return /* binding */ Badge; },\n/* harmony export */   badgeVariants: function() { return /* binding */ badgeVariants; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(app-pages-browser)/./node_modules/.pnpm/class-variance-authority@0.7.1/node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n\n\n\n\nconst badgeVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex font-black border p-2\", {\n    variants: {\n        variant: {\n            default: \"border-primary bg-transparent\",\n            primary: \"text-curious-blue-500 border border-bltext-curious-blue-500 bg-bltext-curious-blue-100\",\n            success: \"text-bright-turquoise-600 border-bright-turquoise-600 bg-bright-turquoise-100\",\n            warning: \"text-fire-bush-500 border-fire-bush-500 bg-fire-bush-100\",\n            destructive: \"text-destructive border-destructive bg-cinnabar-200\",\n            outline: \"text-foreground border-border\",\n            secondary: \"border bg-muted text-muted-foreground w-fit rounded-lg p-2\"\n        },\n        type: {\n            normal: \"size-fit rounded-lg\",\n            circle: \"items-center rounded-full justify-center h-9 w-9\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        type: \"circle\"\n    }\n});\nfunction Badge(param) {\n    let { className, variant, type, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(badgeVariants({\n            variant,\n            type\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\badge.tsx\",\n        lineNumber: 37,\n        columnNumber: 9\n    }, this);\n}\n_c = Badge;\n\nvar _c;\n$RefreshReg$(_c, \"Badge\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/badge.tsx\n"));

/***/ })

});