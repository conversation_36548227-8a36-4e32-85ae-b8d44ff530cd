'use client'
import React, { useEffect, useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import {
    Popover,
    PopoverContent,
    PopoverTrigger,
} from '@/components/ui/popover'
import { MessageSquare, Building2 } from 'lucide-react'
import Loading from '@/app/loading'
import Link from 'next/link'
import { getSupplier } from '@/app/lib/actions'
import { useLazyQuery } from '@apollo/client'
import { GET_SUPPLIER } from '@/app/lib/graphQL/query'
import { isEmpty, trim } from 'lodash'
import { DataTable, createColumns } from '@/components/filteredTable'
import { DataTableSortHeader } from '@/components/data-table-sort-header'
import { ListHeader } from '@/components/ui/list-header'
import { SupplierListFilterActions } from '@/components/filter/components/supplier-list-actions'
import { P } from '@/components/ui'

export default function SupplierList() {
    const [suppliers, setSuppliers] = useState([] as any)
    const [filter, setFilter] = useState({} as SearchFilter)
    const [keywordFilter, setKeywordFilter] = useState([] as any)
    // getSupplier(setSuppliers)

    const [isLoading, setIsLoading] = useState(true)
    const [querySupplier] = useLazyQuery(GET_SUPPLIER, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readSuppliers.nodes
            if (data) {
                setSuppliers(data)
            }
        },
        onError: (error: any) => {
            console.error('querySupplier error', error)
        },
    })
    useEffect(() => {
        if (isLoading) {
            loadSupplier()
            setIsLoading(false)
        }
    }, [isLoading])
    const loadSupplier = async (
        searchFilter: SearchFilter = {},
        searchkeywordFilter: any = keywordFilter,
    ) => {
        if (searchkeywordFilter.length > 0) {
            const promises = searchkeywordFilter.map(
                async (keywordFilter: any) => {
                    return await querySupplier({
                        variables: {
                            filter: { ...searchFilter, ...keywordFilter },
                        },
                    })
                },
            )
            let responses = await Promise.all(promises)
            // filter out empty results
            responses = responses.filter(
                (r: any) => r.data.readSuppliers.nodes.length > 0,
            )
            // flatten results
            responses = responses.flatMap(
                (r: any) => r.data.readSuppliers.nodes,
            )
            // filter out duplicates
            responses = responses.filter(
                (value: any, index: any, self: any) =>
                    self.findIndex((v: any) => v.id === value.id) === index,
            )
            setSuppliers(responses)
        } else {
            await querySupplier({
                variables: {
                    filter: searchFilter,
                },
            })
        }
    }
    const handleFilterOnChange = ({ type, data }: any) => {
        const searchFilter: SearchFilter = { ...filter }
        let keyFilter = keywordFilter
        if (type === 'keyword') {
            if (!isEmpty(trim(data.value))) {
                keyFilter = [
                    { name: { contains: data.value } },
                    { website: { contains: data.value } },
                    { phone: { contains: data.value } },
                    { email: { contains: data.value } },
                    { address: { contains: data.value } },
                ]
            } else {
                keyFilter = []
            }
        }

        setFilter(searchFilter)
        setKeywordFilter(keyFilter)
        loadSupplier(searchFilter, keyFilter)
    }
    const columns = createColumns([
        {
            accessorKey: 'title',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Suppliers" />
            ),
            cellAlignment: 'left',
            cell: ({ row }: { row: any }) => {
                const supplier: any = row.original
                return (
                    <div className="space-y-1">
                        <div className="font-medium">
                            <Link
                                href={`/inventory/suppliers/view?id=${supplier.id}`}
                                className="hover:underline">
                                {supplier.name}
                            </Link>
                            {supplier.phone && (
                                <span className="ml-3 text-sm text-muted-foreground">
                                    {supplier.phone}
                                </span>
                            )}
                        </div>
                        {supplier.email && (
                            <div className="text-sm text-muted-foreground">
                                {supplier.email}
                            </div>
                        )}
                    </div>
                )
            },
            sortingFn: (rowA: any, rowB: any) => {
                const valueA = rowA?.original?.name || ''
                const valueB = rowB?.original?.name || ''
                return valueA.localeCompare(valueB)
            },
        },
        {
            accessorKey: 'address',
            header: 'Address',
            cellAlignment: 'left',
            cell: ({ row }: { row: any }) => {
                const supplier = row.original
                return <div className="text-sm">{supplier.address || '-'}</div>
            },
        },
        {
            accessorKey: 'website',
            header: 'Website',
            cellAlignment: 'left',
            cell: ({ row }: { row: any }) => {
                const supplier = row.original
                return supplier.website ? (
                    <a
                        href={`https://${supplier.website}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-sm text-blue-600 hover:underline">
                        {supplier.website}
                    </a>
                ) : (
                    <span className="text-sm text-muted-foreground">-</span>
                )
            },
        },
        {
            accessorKey: 'contactPeople',
            header: 'Contact People',
            cellAlignment: 'left',
            cell: ({ row }: { row: any }) => {
                const supplier = row.original
                return (
                    <div className="space-y-1 text-sm">
                        {supplier.phone && <div>{supplier.phone}</div>}
                        {supplier.email && (
                            <div className="text-muted-foreground">
                                {supplier.email}
                            </div>
                        )}
                        {!supplier.phone && !supplier.email && (
                            <span className="text-muted-foreground">-</span>
                        )}
                    </div>
                )
            },
        },
        {
            accessorKey: 'notes',
            header: 'Notes',
            cellAlignment: 'right',
            cell: ({ row }: { row: any }) => {
                const supplier = row.original
                return (
                    <Popover>
                        <PopoverTrigger asChild>
                            <Button
                                variant="ghost"
                                size="sm"
                                className="h-8 w-8 p-0">
                                <MessageSquare className="h-4 w-4" />
                            </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-80">
                            <div className="space-y-2">
                                <P className="font-medium text-sm">Notes</P>
                                <P className="text-sm text-muted-foreground">
                                    {supplier.notes || 'No notes available'}
                                </P>
                            </div>
                        </PopoverContent>
                    </Popover>
                )
            },
        },
    ])

    return (
        <>
            <ListHeader
                icon={
                    <Building2 className="h-12 w-12 ring-1 p-1 rounded-full" />
                }
                title="All suppliers"
                actions={<SupplierListFilterActions />}
            />
            <div className="mt-16">
                {isLoading ? (
                    <Loading />
                ) : (
                    <DataTable
                        columns={columns}
                        data={suppliers}
                        showToolbar={true}
                        pageSize={20}
                        onChange={handleFilterOnChange}
                    />
                )}
            </div>
        </>
    )
}
