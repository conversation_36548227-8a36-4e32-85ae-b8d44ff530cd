'use client'
import React, { useEffect, useState } from 'react'
import { getDashboardVesselList, getInventoryList } from '@/app/lib/actions'
import Link from 'next/link'

import { useLazyQuery } from '@apollo/client'
import { READ_ONE_SEALOGS_MEMBER, VESSEL_STATUS } from '@/app/lib/graphQL/query'
import { ArrowLeft, MoreHorizontal } from 'lucide-react'
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { useSidebar } from '@/components/ui/sidebar'
import { DataTable } from '@/components/filteredTable'
import VesselIcon from '../../vessels/vesel-icon'
import { Vessel } from '../../../../../types/vessel'
import {
    SealogsCrewIcon,
    SealogsDocumentLockerIcon,
    SealogsLogbookIcon,
    SealogsMaintenanceIcon,
    SealogsTrainingIcon,
    SealogsVesselsIcon,
} from '@/app/lib/icons'
import { X } from 'lucide-react'
import VesselsStatusEdit from '../../vessels/status-edit-popover'
import VesselStatusChart from './vessel-status-chart'
import { subMonths } from 'date-fns'
import { H1 } from '@/components/ui/typography'
export default function Vessels() {
    const [vesselList, setVesselList] = useState<Vessel[]>([])
    const [filteredVesselList, setFilteredVesselList] = useState<Vessel[]>([])
    const [currentDepartment, setCurrentDepartment] = useState<any>(false)
    const [displayEditStatus, setDisplayEditStatus] = useState(false)
    const [vessel, setVessel] = useState<any>(false)

    const [querySeaLogsMembers] = useLazyQuery(READ_ONE_SEALOGS_MEMBER, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readOneSeaLogsMember
            if (data) {
                setCurrentDepartment(
                    data.departments.nodes.flatMap(
                        (department: any) => department.basicComponents.nodes,
                    ),
                )
                if (
                    data.departments.nodes.flatMap(
                        (department: any) => department.basicComponents.nodes,
                    ).length === 0
                ) {
                    setCurrentDepartment(true)
                }
            }
        },
        onError: (error: any) => {
            console.error('querySeaLogsMembers error', error)
        },
    })

    useEffect(() => {
        querySeaLogsMembers({
            variables: {
                filter: { id: { eq: +(localStorage.getItem('userId') ?? 0) } },
            },
        })
    }, [])

    useEffect(() => {
        if (currentDepartment && vesselList) {
            if (
                currentDepartment === true ||
                localStorage.getItem('useDepartment') !== 'true'
            ) {
                setFilteredVesselList(vesselList)
            } else {
                setFilteredVesselList(
                    vesselList.filter((vessel: any) =>
                        currentDepartment.some(
                            (department: any) => department.id === vessel.id,
                        ),
                    ),
                )
                setFilteredVesselList(vesselList)
            }
        }
    }, [currentDepartment, vesselList])

    const handleSetVessels = (vessels: any) => {
        const activeVessels = vessels.filter(
            (vessel: any) => vessel.showOnDashboard,
        )
        console.info('Active Vessels', activeVessels)
        setVesselList(activeVessels)
    }

    getDashboardVesselList(handleSetVessels)

    const { isMobile } = useSidebar()

    const columns = [
        {
            accessorKey: 'title',
            header: '',
            cell: ({ row }: { row: any }) => {
                const vessel = row.original
                const [vesselStatus, setVesselStatus] = useState<any>(null)

                useEffect(() => {
                    getVesselStatus({ variables: { id: +vessel.id } })
                }, [vessel.id])

                const [getVesselStatus] = useLazyQuery(VESSEL_STATUS, {
                    fetchPolicy: 'cache-and-network',
                    onCompleted: (response) => {
                        const data = response.readVesselStatuss.nodes
                        if (data) {
                            setVesselStatus(data[0])
                        }
                    },
                    onError: (error) => {
                        console.error('Error getting vessel status', error)
                    },
                })
                return (
                    <>
                        {vesselStatus &&
                        vesselStatus.status !== 'OutOfService' ? (
                            <Link
                                className="flex flex-row gap-2 whitespace-nowrap items-center"
                                href={`/vessel/info?id=${vessel.id}&name=${vessel.title}`}>
                                <div className="border-2 border-border rounded-full hidden small:inline-block">
                                    <VesselIcon vessel={vessel} />
                                </div>
                                <div className="grid">
                                    <span className="font-medium truncate hover:text-curious-blue-800">
                                        {vessel.title}
                                    </span>

                                    {vessel.logentryID !== 0 ? (
                                        <div className="text-curious-blue-600 text-[10px]">
                                            ON VOYAGE
                                        </div>
                                    ) : (
                                        <div className="text-accent text-[10px]">
                                            READY FOR VOYAGE
                                        </div>
                                    )}
                                </div>
                            </Link>
                        ) : (
                            <Link
                                className="flex flex-row gap-2 whitespace-nowrap items-center"
                                href={`/vessel/info?id=${vessel.id}&name=${vessel.title}`}>
                                <div className="relative hidden small:inline-block overflow-hidden border-2 border-cinnabar-600 rounded-full">
                                    <VesselIcon vessel={vessel} />
                                    <div className="absolute inset-0 bg-red-vivid-700 opacity-50 rounded-full">
                                        {'        '}
                                    </div>
                                </div>
                                <div className="grid">
                                    <span className="font-medium opacity-50 truncate">
                                        {vessel.title}
                                    </span>
                                    <div className="inline-block text-[10px] text-destructive">
                                        OUT OF SERVICE
                                    </div>
                                </div>
                            </Link>
                        )}
                    </>
                )
            },
        },
        {
            accessorKey: 'trainingsDue',
            header: 'Training',
            cell: ({ row }: { row: any }) => {
                const vessel = row.original

                return (
                    <div>
                        {vessel.trainingsDue > 0 ? (
                            <div className={`alert !rounded-full flex w-8 h-8`}>
                                {vessel.trainingsDue}
                            </div>
                        ) : (
                            <div
                                className={`text-bright-turquoise-600 border bg-bright-turquoise-100 border-bright-turquoise-600 items-center justify-center p-2 rounded-full flex w-8 h-8`}>
                                <svg
                                    className={`h-5 w-5`}
                                    viewBox="0 0 20 20"
                                    fill="#00a396"
                                    aria-hidden="true">
                                    <path
                                        fillRule="evenodd"
                                        d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z"
                                        clipRule="evenodd"
                                    />
                                </svg>
                            </div>
                        )}
                    </div>
                )
            },
        },
        {
            accessorKey: 'tasksDue',
            header: 'Tasks',
            cell: ({ row }: { row: any }) => {
                const vessel = row.original

                return (
                    <div>
                        {vessel.tasksDue > 0 ? (
                            <div className={`alert !rounded-full flex w-8 h-8`}>
                                {vessel.tasksDue}
                            </div>
                        ) : (
                            <div
                                className={`text-bright-turquoise-600 border bg-bright-turquoise-100 border-bright-turquoise-600 items-center justify-center p-2 rounded-full flex w-8 h-8`}>
                                <svg
                                    className={`h-5 w-5`}
                                    viewBox="0 0 20 20"
                                    fill="#27AB83"
                                    aria-hidden="true">
                                    <path
                                        fillRule="evenodd"
                                        d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z"
                                        clipRule="evenodd"
                                    />
                                </svg>
                            </div>
                        )}
                    </div>
                )
            },
        },
        {
            id: 'actions',
            enableHiding: false,
            cell: ({ row }: { row: any }) => {
                const vessel = row.original
                const [vesselStatus, setVesselStatus] = useState<any>(null)

                useEffect(() => {
                    getVesselStatus({ variables: { id: +vessel.id } })
                }, [vessel.id])

                const [getVesselStatus] = useLazyQuery(VESSEL_STATUS, {
                    fetchPolicy: 'cache-and-network',
                    onCompleted: (response) => {
                        const data = response.readVesselStatuss.nodes
                        if (data) {
                            setVesselStatus(data[0])
                        }
                    },
                    onError: (error) => {
                        console.error('Error getting vessel status', error)
                    },
                })
                return (
                    <div className="flex items-center h-full">
                        <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                                <MoreHorizontal />
                            </DropdownMenuTrigger>
                            <DropdownMenuContent
                                className="rounded-lg"
                                side={isMobile ? 'bottom' : 'right'}
                                align={isMobile ? 'end' : 'start'}>
                                <Link href={`/vessel/info?id=${vessel.id}`}>
                                    <DropdownMenuItem>
                                        <ArrowLeft className="icons h-6 w-6" />
                                        <span>View vessel</span>
                                    </DropdownMenuItem>
                                </Link>
                                <Link
                                    href={`/vessel/info?id=${vessel.id}&tab=maintenance`}>
                                    <DropdownMenuItem>
                                        <SealogsMaintenanceIcon className="icons h-6 w-6" />
                                        <span>Maintenance</span>
                                    </DropdownMenuItem>
                                </Link>
                                <Link
                                    href={`/vessel/info?id=${vessel.id}&tab=crew`}>
                                    <DropdownMenuItem>
                                        <SealogsCrewIcon className="icons h-6 w-6" />
                                        <span>Crew</span>
                                    </DropdownMenuItem>
                                </Link>
                                <Link
                                    href={`/vessel/info?id=${vessel.id}&tab=crew_training`}>
                                    <DropdownMenuItem>
                                        <SealogsTrainingIcon className="icons h-6 w-6" />
                                        <span>Training & drills</span>
                                    </DropdownMenuItem>
                                </Link>
                                {vessel.logentryID !== 0 ? (
                                    <Link
                                        href={`/log-entries/view?vesselID=${vessel.id}&logentryID=${vessel.logentryID}`}>
                                        <DropdownMenuItem>
                                            <SealogsLogbookIcon className="icons h-6 w-6" />
                                            <span>Open Logbook entry</span>
                                        </DropdownMenuItem>
                                    </Link>
                                ) : (
                                    <Link href={`/vessel/info?id=${vessel.id}`}>
                                        <DropdownMenuItem>
                                            <SealogsLogbookIcon className="icons h-6 w-6" />
                                            <span>Logbook entry</span>
                                        </DropdownMenuItem>
                                    </Link>
                                )}
                                <Link
                                    href={`/vessel/info?id=${vessel.id}&tab=documents`}>
                                    <DropdownMenuItem>
                                        <SealogsDocumentLockerIcon className="icons h-6 w-6" />
                                        <span>Documents</span>
                                    </DropdownMenuItem>
                                </Link>
                                <DropdownMenuSeparator />
                                <div
                                    className="text-red-vivid-400"
                                    onClick={() => {
                                        setDisplayEditStatus(true),
                                            setVessel(vessel)
                                    }}>
                                    <DropdownMenuItem>
                                        <X className="border rounded-full w-5 h-5" />

                                        {vesselStatus &&
                                        vesselStatus.status !== 'OutOfService'
                                            ? 'Take vessel out of service'
                                            : 'Change vessel status'}
                                    </DropdownMenuItem>
                                </div>
                            </DropdownMenuContent>
                        </DropdownMenu>
                    </div>
                )
            },
        },
    ]

    return (
        <>
            <div className="flex py-3 items-baseline">
                <SealogsVesselsIcon
                    className={`h-12 w-12 ring-1 p-1 rounded-full`}
                />
                <Link href={`/vessel`}>
                    <H1 className="pl-4">Vessels</H1>
                </Link>
            </div>
            <VesselStatusChart
                startMonth={subMonths(new Date(), 5)}
                endMonth={new Date()}
            />
            {filteredVesselList.length > 0 && (
                <>
                    <DataTable
                        columns={columns}
                        data={filteredVesselList}
                        showToolbar={false}
                        className={'p-0 border-0 shadow-none'}
                    />
                    <div className="mt-4 items-center rounded-lg gap-4 xs:gap-0 bg-curious-blue-50 border border-curious-blue-100 p-5 text-center">
                        <Link
                            href={`/vessel`}
                            className="text-accent uppercase hover:text-primary text-xs">
                            See all vessels
                        </Link>
                    </div>
                </>
            )}
            <VesselsStatusEdit
                vessel={vessel}
                display={displayEditStatus}
                setDisplay={setDisplayEditStatus}
            />
        </>
    )
}
