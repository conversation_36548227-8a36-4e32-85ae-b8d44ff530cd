'use client'
import React, { useEffect, useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import {
    Popover,
    PopoverContent,
    PopoverTrigger,
} from '@/components/ui/popover'
import { getSupplier, isOverDueTask } from '@/app/lib/actions'
import { useLazyQuery } from '@apollo/client'
import Loading from '@/app/loading'
import Link from 'next/link'
import { isEmpty, trim } from 'lodash'
import { GET_INVENTORIES } from '@/app/lib/graphQL/query'
import { useRouter, usePathname, useSearchParams } from 'next/navigation'
import { DataTable, createColumns } from '@/components/filteredTable'
import { DataTableSortHeader } from '@/components/data-table-sort-header'
import { ListHeader } from '@/components/ui/list-header'
import { SealogsInventoryIcon } from '@/app/lib/icons/SealogsInventoryIcon'
import { InventoryFilterActions } from '@/components/filter/components/inventory-actions'
import { Badge, H4, P } from '@/components/ui'

export default function InventoryList() {
    const router = useRouter()
    const pathname = usePathname()
    const searchParams = useSearchParams()

    const [inventories, setInventories] = useState<any[]>([])
    const [suppliers, setSuppliers] = useState<any>(null)
    const [filter, setFilter] = useState({} as SearchFilter)
    const [keywordFilter, setKeywordFilter] = useState<any[]>([])
    const [isLoading, setIsLoading] = useState(true)
    const [page, setPage] = useState(1)
    const [maxPage, setMaxPage] = useState(1)
    const limit = 20

    // Query inventories via GraphQL.
    const [queryInventories] = useLazyQuery(GET_INVENTORIES, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readInventories.nodes
            if (data) {
                setInventories(data)
                setMaxPage(
                    Math.ceil(
                        response.readInventories.pageInfo.totalCount / limit,
                    ),
                )
            }
        },
        onError: (error: any) => {
            console.error('queryInventories error', error)
        },
    })

    // Load supplier data.
    getSupplier(setSuppliers)

    // Function to load inventories.
    const loadInventories = async (
        searchFilter: SearchFilter = {},
        searchkeywordFilter: any[] = keywordFilter,
    ) => {
        if (searchkeywordFilter.length > 0) {
            const promises = searchkeywordFilter.map(
                async (keywordFilter: any) => {
                    return await queryInventories({
                        variables: {
                            filter: { ...searchFilter, ...keywordFilter },
                            offset: (page - 1) * limit,
                        },
                    })
                },
            )
            let responses = await Promise.all(promises)
            responses = responses.filter(
                (r: any) => r.data.readInventories.nodes.length > 0,
            )
            responses = responses.flatMap(
                (r: any) => r.data.readInventories.nodes,
            )
            responses = responses.filter(
                (value: any, index: number, self: any) =>
                    self.findIndex((v: any) => v.id === value.id) === index,
            )
            setInventories(responses)
        } else {
            await queryInventories({
                variables: {
                    filter: searchFilter,
                    offset: (page - 1) * limit,
                },
            })
        }
    }

    // Called when the Filter component changes.
    const handleFilterOnChange = ({ type, data }: any) => {
        const searchFilter: SearchFilter = { ...filter }
        if (type === 'vessel') {
            if (Array.isArray(data) && data.length > 0) {
                searchFilter.vesselID = { in: data.map((item) => +item.value) }
            } else if (data && !Array.isArray(data)) {
                searchFilter.vesselID = { eq: +data.value }
            } else {
                delete searchFilter.vesselID
            }
        }
        if (type === 'supplier') {
            if (Array.isArray(data) && data.length > 0) {
                searchFilter.suppliers = {
                    id: { in: data.map((item) => +item.value) },
                }
            } else if (data && !Array.isArray(data)) {
                searchFilter.suppliers = { id: { in: [+data.value] } }
            } else {
                delete searchFilter.suppliers
            }
        }
        if (type === 'category') {
            if (Array.isArray(data) && data.length > 0) {
                searchFilter.componentCategory = {
                    in: data.map((item) => String(item.value)),
                }
            } else if (data && !Array.isArray(data)) {
                searchFilter.componentCategory = { eq: String(data.value) }
            } else {
                delete searchFilter.componentCategory
            }
        }
        if (type === 'keyword') {
            if (!isEmpty(trim(data?.value))) {
                setKeywordFilter([
                    { item: { contains: data.value } },
                    { title: { contains: data.value } },
                    { productCode: { contains: data.value } },
                    { description: { contains: data.value } },
                    { comments: { contains: data.value } },
                ])
            } else {
                setKeywordFilter([])
            }
        }
        setFilter(searchFilter)
        setPage(1)
        loadInventories(searchFilter, keywordFilter)
    }

    useEffect(() => {
        setPage(1)
        loadInventories(filter, keywordFilter)
        setIsLoading(false)
    }, [filter, page])

    useEffect(() => {
        loadInventories(filter, keywordFilter)
    }, [filter, keywordFilter])

    const columns = createColumns([
        {
            accessorKey: 'title',
            header: 'Item',
            cell: ({ row }: { row: any }) => {
                const inventory = row.original
                return (
                    <Link
                        href={`/inventory/view/?id=${inventory.id}&redirect_to=${pathname}?${searchParams.toString()}&tab=inventory`}
                        className="flex items-center">
                        {inventory.quantity + ' x ' + inventory.item}
                    </Link>
                )
            },
            filterFn: (row: any, columnId: string, filterValue: string) => {
                const inventory = row.original
                const text = (inventory.item || '').toLowerCase()
                return text.includes(filterValue.toLowerCase())
            },
        },
        {
            accessorKey: 'location',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Location" />
            ),
            cell: ({ row }: { row: any }) => {
                const inventory = row.original
                return (
                    <div>
                        {inventory.vessel?.title || inventory.location || 'N/A'}
                    </div>
                )
            },
            cellAlignment: 'left',
            filterFn: (row: any, columnId: string, filterValue: string) => {
                const inventory = row.original
                const loc = (
                    inventory.vessel?.title ||
                    inventory.location ||
                    ''
                ).toLowerCase()
                return loc.includes(filterValue.toLowerCase())
            },
            sortingFn: (rowA: any, rowB: any) => {
                const valueA =
                    rowA?.original?.vessel?.title ||
                    rowA?.original?.location ||
                    ''
                const valueB =
                    rowB?.original?.vessel?.title ||
                    rowB?.original?.location ||
                    ''
                return valueA.localeCompare(valueB)
            },
        },
        {
            accessorKey: 'maintenance',
            header: 'Maintenance',
            cellAlignment: 'left',
            cell: ({ row }: { row: any }) => {
                const inventory = row.original
                return (
                    <div className="flex justify-center items-center">
                        {inventory.maintenanceStatus || 'N/A'}
                    </div>
                )
            },
        },
        {
            accessorKey: 'categories',
            header: 'Categories',
            cellAlignment: 'left',
            cell: ({ row }: { row: any }) => {
                const inventory = row.original
                return (
                    <div className="flex gap-2 items-center">
                        {inventory.categories?.nodes
                            ?.slice(0, 2)
                            .map((cat: any, idx: number) => (
                                <Badge key={String(idx)} type="normal">
                                    {cat.name}
                                </Badge>
                            ))}
                        {inventory.categories?.nodes?.length > 2 && (
                            <Popover>
                                <PopoverTrigger asChild>
                                    <Button variant="outline">
                                        +{' '}
                                        {inventory.categories.nodes.length - 2}{' '}
                                        more
                                    </Button>
                                </PopoverTrigger>
                                <PopoverContent className="w-80">
                                    <div className="space-y-2">
                                        <P className="font-medium text-sm">
                                            All Categories
                                        </P>
                                        <div className="flex flex-wrap gap-2">
                                            {inventory.categories.nodes.map(
                                                (cat: any, idx: number) => (
                                                    <Badge
                                                        key={String(idx)}
                                                        type="normal">
                                                        {cat.name}
                                                    </Badge>
                                                ),
                                            )}
                                        </div>
                                    </div>
                                </PopoverContent>
                            </Popover>
                        )}
                    </div>
                )
            },
        },
        {
            accessorKey: 'suppliers',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Suppliers" />
            ),
            cellAlignment: 'right',
            cell: ({ row }: { row: any }) => {
                const inventory = row.original
                return (
                    <div className="flex flex-col">
                        {inventory.suppliers?.nodes?.map((supplier: any) => (
                            <div key={String(supplier.id)}>
                                <Link
                                    href={`/inventory/suppliers/view?id=${supplier.id}`}>
                                    {supplier.name}
                                </Link>
                            </div>
                        ))}
                    </div>
                )
            },
            filterFn: (row: any, columnId: string, filterValue: string) => {
                const inventory = row.original
                if (!filterValue) return true
                const supplierNames = (inventory.suppliers?.nodes || [])
                    .map((s: any) => s.name.toLowerCase())
                    .join(' ')
                return supplierNames.includes(filterValue.toLowerCase())
            },
            sortingFn: (rowA: any, rowB: any) => {
                const valueA = rowA?.original?.suppliers?.nodes?.[0]?.name || ''
                const valueB = rowB?.original?.suppliers?.nodes?.[0]?.name || ''
                return valueA.localeCompare(valueB)
            },
        },
    ])

    return (
        <>
            <ListHeader
                icon={
                    <SealogsInventoryIcon
                        className={`h-12 w-12 ring-1 p-1 rounded-full`}
                    />
                }
                title="All inventory"
                actions={<InventoryFilterActions />}
            />
            <div className="mt-16">
                {isLoading ? (
                    <Loading />
                ) : (
                    <DataTable
                        columns={columns}
                        data={inventories}
                        showToolbar={true}
                        pageSize={limit}
                        onChange={handleFilterOnChange}
                    />
                )}
            </div>
        </>
    )
}
